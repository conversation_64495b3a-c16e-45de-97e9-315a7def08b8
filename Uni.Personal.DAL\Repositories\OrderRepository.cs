using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Models;

namespace Uni.Personal.DAL.Repositories
{
    public class OrderRepository : UniBaseRepository, IOrderRepository
    {
        public OrderRepository(IUniCommonBaseRepository uniCommonBaseRepository)
            : base(uniCommonBaseRepository)
        {
        }

        public async Task<CommonListPage> GetPageAsync(OrderFilterInput filter)
        {
            return await GetOrderPageAsync(filter);
        }

        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            var orderInfo = await GetOrderInfoAsync(oid);
            return new CommonViewOidInfo { Oid = orderInfo?.Oid };
        }

        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            var orderInfo = new OrderViewInfo { Oid = info.Oid };
            return await SetOrderInfoAsync(orderInfo);
        }

        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            return await DeleteOrderAsync(oid);
        }

        public async Task<CommonListPage> GetOrderPageAsync(OrderFilterInput filter)
        {
            try
            {
                var result = await GetPageAsync("sp_Order_GetPage", filter,
                    param =>
                    {
                        if (filter.OrderId.HasValue)
                            param.Add("@OrderId", filter.OrderId.Value);
                        if (!string.IsNullOrEmpty(filter.OrderCode))
                            param.Add("@OrderCode", filter.OrderCode);
                        if (filter.OrderStatus.HasValue)
                            param.Add("@OrderStatus", filter.OrderStatus.Value);
                        if (filter.OrderType.HasValue)
                            param.Add("@OrderType", filter.OrderType.Value);
                        if (filter.FromDate.HasValue)
                            param.Add("@FromDate", filter.FromDate.Value);
                        if (filter.ToDate.HasValue)
                            param.Add("@ToDate", filter.ToDate.Value);
                        if (!string.IsNullOrEmpty(filter.CustomerName))
                            param.Add("@CustomerName", filter.CustomerName);
                        if (!string.IsNullOrEmpty(filter.CustomerAccount))
                            param.Add("@CustomerAccount", filter.CustomerAccount);

                        return param;
                    });

                return result;
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting order page: {ex.Message}", ex);
            }
        }

        public async Task<OrderViewInfo> GetOrderInfoAsync(Guid? oid)
        {
            try
            {
                if (!oid.HasValue)
                    return new OrderViewInfo();

                var result = await GetFirstOrDefaultAsync<OrderViewInfo>("sp_Order_GetInfo",
                    new { Oid = oid.Value });

                return result ?? new OrderViewInfo();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting order info: {ex.Message}", ex);
            }
        }

        public async Task<BaseValidate<Guid?>> SetOrderInfoAsync(OrderViewInfo info)
        {
            try
            {
                var result = await SetAsync<Guid?>("sp_Order_SetInfo", info);
                return result;
            }
            catch (Exception ex)
            {
                return new BaseValidate<Guid?>
                {
                    valid = false,
                    messages = $"Error saving order: {ex.Message}"
                };
            }
        }

        public async Task<BaseValidate> DeleteOrderAsync(Guid? oid)
        {
            try
            {
                if (!oid.HasValue)
                {
                    return new BaseValidate
                    {
                        valid = false,
                        messages = "Order ID is required"
                    };
                }

                var result = await SetAsync("sp_Order_Delete", new { Oid = oid.Value });
                return result;
            }
            catch (Exception ex)
            {
                return new BaseValidate
                {
                    valid = false,
                    messages = $"Error deleting order: {ex.Message}"
                };
            }
        }

        public async Task<BaseValidate> UpdateOrderStatusAsync(Guid? oid, int status)
        {
            try
            {
                if (!oid.HasValue)
                {
                    return new BaseValidate
                    {
                        valid = false,
                        messages = "Order ID is required"
                    };
                }

                var result = await SetAsync("sp_Order_UpdateStatus", new { Oid = oid.Value, Status = status });

                return result;
            }
            catch (Exception ex)
            {
                return new BaseValidate
                {
                    valid = false,
                    messages = $"Error updating order status: {ex.Message}"
                };
            }
        }

        public async Task<List<OrderViewInfo>> GetOrdersByCustomerAsync(string customerAccount)
        {
            try
            {
                if (string.IsNullOrEmpty(customerAccount))
                    return [];

                var result = await GetListAsync<OrderViewInfo>("sp_Order_GetByCustomer", new { CustomerAccount = customerAccount });

                return result ?? [];
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting orders by customer: {ex.Message}", ex);
            }
        }
    }
}
