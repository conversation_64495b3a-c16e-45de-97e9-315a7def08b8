using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Models;

namespace Uni.Personal.DAL.Repositories
{
    public interface IOrderRepository : IBaseRepository<OrderFilterInput>
    {
        Task<CommonListPage> GetOrderPageAsync(OrderFilterInput filter);
        Task<OrderViewInfo> GetOrderInfoAsync(Guid? oid);
        Task<BaseValidate<Guid?>> SetOrderInfoAsync(OrderViewInfo info);
        Task<BaseValidate> DeleteOrderAsync(Guid? oid);
        Task<BaseValidate> UpdateOrderStatusAsync(Guid? oid, int status);
        Task<List<OrderViewInfo>> GetOrdersByCustomerAsync(string customerAccount);
    }
}
