using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Models;

namespace Uni.Personal.DAL.Repositories
{
    public class OrderRepository : UniBaseRepository, IOrderRepository
    {
        public OrderRepository(IUniCommonBaseRepository uniCommonBaseRepository) 
            : base(uniCommonBaseRepository)
        {
        }

        public async Task<CommonListPage> GetPageAsync(OrderFilterInput filter)
        {
            return await GetOrderPageAsync(filter);
        }

        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            var orderInfo = await GetOrderInfoAsync(oid);
            return new CommonViewOidInfo { Oid = orderInfo?.Oid };
        }

        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            var orderInfo = new OrderViewInfo { Oid = info.Oid };
            return await SetOrderInfoAsync(orderInfo);
        }

        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            return await DeleteOrderAsync(oid);
        }

        public async Task<CommonListPage> GetOrderPageAsync(OrderFilterInput filter)
        {
            try
            {
                var data = await GetDataListPage<CommonDataPage>("sp_Order_GetPage", filter, 
                    param =>
                    {
                        if (filter.OrderId.HasValue)
                            param.Add("@OrderId", filter.OrderId.Value);
                        if (!string.IsNullOrEmpty(filter.OrderCode))
                            param.Add("@OrderCode", filter.OrderCode);
                        if (filter.OrderStatus.HasValue)
                            param.Add("@OrderStatus", filter.OrderStatus.Value);
                        if (filter.OrderType.HasValue)
                            param.Add("@OrderType", filter.OrderType.Value);
                        if (filter.FromDate.HasValue)
                            param.Add("@FromDate", filter.FromDate.Value);
                        if (filter.ToDate.HasValue)
                            param.Add("@ToDate", filter.ToDate.Value);
                        if (!string.IsNullOrEmpty(filter.CustomerName))
                            param.Add("@CustomerName", filter.CustomerName);
                        if (!string.IsNullOrEmpty(filter.CustomerAccount))
                            param.Add("@CustomerAccount", filter.CustomerAccount);
                        
                        return param;
                    });

                return new CommonListPage
                {
                    recordsTotal = data.recordsTotal,
                    recordsFiltered = data.recordsFiltered,
                    dataList = data.dataList
                };
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting order page: {ex.Message}", ex);
            }
        }

        public async Task<OrderViewInfo> GetOrderInfoAsync(Guid? oid)
        {
            try
            {
                if (!oid.HasValue)
                    return new OrderViewInfo();

                var result = await GetInfoAsync<OrderViewInfo>("sp_Order_GetInfo", 
                    param =>
                    {
                        param.Add("@Oid", oid.Value);
                        return param;
                    });

                return result ?? new OrderViewInfo();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting order info: {ex.Message}", ex);
            }
        }

        public async Task<BaseValidate<Guid?>> SetOrderInfoAsync(OrderViewInfo info)
        {
            try
            {
                var result = await SetInfoAsync<BaseValidate<Guid?>>("sp_Order_SetInfo", info,
                    param =>
                    {
                        if (info.Oid.HasValue)
                            param.Add("@Oid", info.Oid.Value);
                        if (!string.IsNullOrEmpty(info.OrderCode))
                            param.Add("@OrderCode", info.OrderCode);
                        param.Add("@OrderStatus", info.OrderStatus);
                        param.Add("@OrderType", info.OrderType);
                        if (info.OrderDate.HasValue)
                            param.Add("@OrderDate", info.OrderDate.Value);
                        param.Add("@Amount", info.Amount);
                        if (!string.IsNullOrEmpty(info.CustomerName))
                            param.Add("@CustomerName", info.CustomerName);
                        if (!string.IsNullOrEmpty(info.CustomerAccount))
                            param.Add("@CustomerAccount", info.CustomerAccount);
                        if (!string.IsNullOrEmpty(info.BankName))
                            param.Add("@BankName", info.BankName);
                        if (!string.IsNullOrEmpty(info.Description))
                            param.Add("@Description", info.Description);
                        
                        return param;
                    });

                return result;
            }
            catch (Exception ex)
            {
                return new BaseValidate<Guid?>
                {
                    valid = false,
                    messages = $"Error saving order: {ex.Message}"
                };
            }
        }

        public async Task<BaseValidate> DeleteOrderAsync(Guid? oid)
        {
            try
            {
                if (!oid.HasValue)
                {
                    return new BaseValidate
                    {
                        valid = false,
                        messages = "Order ID is required"
                    };
                }

                var result = await SetAsync<BaseValidate>("sp_Order_Delete",
                    param =>
                    {
                        param.Add("@Oid", oid.Value);
                        return param;
                    });

                return result;
            }
            catch (Exception ex)
            {
                return new BaseValidate
                {
                    valid = false,
                    messages = $"Error deleting order: {ex.Message}"
                };
            }
        }

        public async Task<BaseValidate> UpdateOrderStatusAsync(Guid? oid, int status)
        {
            try
            {
                if (!oid.HasValue)
                {
                    return new BaseValidate
                    {
                        valid = false,
                        messages = "Order ID is required"
                    };
                }

                var result = await SetAsync<BaseValidate>("sp_Order_UpdateStatus",
                    param =>
                    {
                        param.Add("@Oid", oid.Value);
                        param.Add("@Status", status);
                        return param;
                    });

                return result;
            }
            catch (Exception ex)
            {
                return new BaseValidate
                {
                    valid = false,
                    messages = $"Error updating order status: {ex.Message}"
                };
            }
        }

        public async Task<List<OrderViewInfo>> GetOrdersByCustomerAsync(string customerAccount)
        {
            try
            {
                if (string.IsNullOrEmpty(customerAccount))
                    return new List<OrderViewInfo>();

                var result = await GetListAsync<OrderViewInfo>("sp_Order_GetByCustomer",
                    param =>
                    {
                        param.Add("@CustomerAccount", customerAccount);
                        return param;
                    });

                return result ?? new List<OrderViewInfo>();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting orders by customer: {ex.Message}", ex);
            }
        }
    }
}
