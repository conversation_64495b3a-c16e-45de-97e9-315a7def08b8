using UNI.Common.CommonBase;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.DAL.Models;
using Uni.Personal.DAL.Repositories;

namespace Uni.Personal.BLL.Services
{
    public class OrderService : UniBaseService, IOrderService
    {
        private readonly IOrderRepository _orderRepository;

        public OrderService(IOrderRepository orderRepository)
        {
            _orderRepository = orderRepository;
        }

        public async Task<BaseResponse<CommonListPage>> GetOrderPageAsync(OrderFilterInput filter)
        {
            try
            {
                var result = await _orderRepository.GetOrderPageAsync(filter);
                return new BaseResponse<CommonListPage>(ApiResult.Success, result);
            }
            catch (Exception ex)
            {
                return new BaseResponse<CommonListPage>(ApiResult.Error, default, ex.Message);
            }
        }

        public async Task<BaseResponse<OrderViewInfo>> GetOrderInfoAsync(Guid? oid)
        {
            try
            {
                if (!oid.HasValue)
                {
                    return new BaseResponse<OrderViewInfo>(ApiResult.Error, default, "Order ID is required");
                }

                var result = await _orderRepository.GetOrderInfoAsync(oid);
                return new BaseResponse<OrderViewInfo>(ApiResult.Success, result);
            }
            catch (Exception ex)
            {
                return new BaseResponse<OrderViewInfo>(ApiResult.Error, default, ex.Message);
            }
        }

        public async Task<BaseResponse<Guid?>> CreateOrderAsync(OrderViewInfo orderInfo)
        {
            try
            {
                // Validate order info
                var validationResult = await ValidateOrderAsync(orderInfo);
                if (validationResult.StatusCode != Constants.CodeStatusSuccess)
                {
                    return new BaseResponse<Guid?>(ApiResult.Error, default, validationResult.Message);
                }

                // Set creation info
                orderInfo.Oid = null; // Ensure it's a new order
                orderInfo.CreatedDate = DateTime.UtcNow;

                var result = await _orderRepository.SetOrderInfoAsync(orderInfo);

                if (result.valid)
                {
                    return new BaseResponse<Guid?>(ApiResult.Success, result.Data);
                }
                else
                {
                    return new BaseResponse<Guid?>(ApiResult.Error, default, result.messages);
                }
            }
            catch (Exception ex)
            {
                return new BaseResponse<Guid?>(ApiResult.Error, default, ex.Message);
            }
        }

        public async Task<BaseResponse<Guid?>> UpdateOrderAsync(OrderViewInfo orderInfo)
        {
            try
            {
                if (!orderInfo.Oid.HasValue)
                {
                    return new BaseResponse<Guid?>(ApiResult.Error, default, "Order ID is required for update");
                }

                // Validate order info
                var validationResult = await ValidateOrderAsync(orderInfo);
                if (validationResult.StatusCode != Constants.CodeStatusSuccess)
                {
                    return new BaseResponse<Guid?>(ApiResult.Error, default, validationResult.Message);
                }

                // Set update info
                orderInfo.UpdatedDate = DateTime.UtcNow;

                var result = await _orderRepository.SetOrderInfoAsync(orderInfo);

                if (result.valid)
                {
                    return new BaseResponse<Guid?>(ApiResult.Success, result.Data);
                }
                else
                {
                    return new BaseResponse<Guid?>(ApiResult.Error, default, result.messages);
                }
            }
            catch (Exception ex)
            {
                return new BaseResponse<Guid?>(ApiResult.Error, default, ex.Message);
            }
        }

        public async Task<BaseResponse> DeleteOrderAsync(Guid? oid)
        {
            try
            {
                if (!oid.HasValue)
                {
                    return new BaseResponse(ApiResult.Error, "Order ID is required");
                }

                var result = await _orderRepository.DeleteOrderAsync(oid);

                if (result.valid)
                {
                    return new BaseResponse(ApiResult.Success);
                }
                else
                {
                    return new BaseResponse(ApiResult.Error, result.messages);
                }
            }
            catch (Exception ex)
            {
                return new BaseResponse(ApiResult.Error, ex.Message);
            }
        }

        public async Task<BaseResponse> UpdateOrderStatusAsync(Guid? oid, int status)
        {
            try
            {
                if (!oid.HasValue)
                {
                    return new BaseResponse(ApiResult.Error, "Order ID is required");
                }

                // Validate status
                if (!Enum.IsDefined(typeof(OrderStatusEnum), status))
                {
                    return new BaseResponse(ApiResult.Error, "Invalid order status");
                }

                var result = await _orderRepository.UpdateOrderStatusAsync(oid, status);

                if (result.valid)
                {
                    return new BaseResponse(ApiResult.Success);
                }
                else
                {
                    return new BaseResponse(ApiResult.Error, result.messages);
                }
            }
            catch (Exception ex)
            {
                return new BaseResponse(ApiResult.Error, ex.Message);
            }
        }

        public async Task<BaseResponse<List<OrderViewInfo>>> GetOrdersByCustomerAsync(string customerAccount)
        {
            try
            {
                if (string.IsNullOrEmpty(customerAccount))
                {
                    return new BaseResponse<List<OrderViewInfo>>(ApiResult.Error, default, "Customer account is required");
                }

                var result = await _orderRepository.GetOrdersByCustomerAsync(customerAccount);
                return new BaseResponse<List<OrderViewInfo>>(ApiResult.Success, result);
            }
            catch (Exception ex)
            {
                return new BaseResponse<List<OrderViewInfo>>(ApiResult.Error, default, ex.Message);
            }
        }

        public async Task<BaseResponse> ValidateOrderAsync(OrderViewInfo orderInfo)
        {
            try
            {
                var errors = new List<string>();

                // Validate required fields
                if (string.IsNullOrEmpty(orderInfo.CustomerName))
                {
                    errors.Add("Customer name is required");
                }

                if (string.IsNullOrEmpty(orderInfo.CustomerAccount))
                {
                    errors.Add("Customer account is required");
                }

                if (orderInfo.Amount <= 0)
                {
                    errors.Add("Amount must be greater than zero");
                }

                if (!Enum.IsDefined(typeof(OrderStatusEnum), orderInfo.OrderStatus))
                {
                    errors.Add("Invalid order status");
                }

                if (!Enum.IsDefined(typeof(OrderType), orderInfo.OrderType))
                {
                    errors.Add("Invalid order type");
                }

                if (errors.Count != 0)
                {
                    return new BaseResponse(ApiResult.Error, string.Join("; ", errors));
                }

                return new BaseResponse(ApiResult.Success);
            }
            catch (Exception ex)
            {
                return new BaseResponse(ApiResult.Error, ex.Message);
            }
        }
    }
}
