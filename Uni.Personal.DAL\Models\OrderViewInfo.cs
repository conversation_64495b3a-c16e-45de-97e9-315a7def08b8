using UNI.Model;

namespace Uni.Personal.DAL.Models
{
    public class OrderViewInfo : CommonViewOidInfo
    {
        public string? OrderCode { get; set; }
        public int OrderStatus { get; set; }
        public int OrderType { get; set; }
        public DateTime? OrderDate { get; set; }
        public decimal Amount { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerAccount { get; set; }
        public string? BankName { get; set; }
        public string? Description { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public string? CreatedBy { get; set; }
        public string? UpdatedBy { get; set; }
    }
}
