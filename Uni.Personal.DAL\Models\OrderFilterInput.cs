using UNI.Model;

namespace Uni.Personal.DAL.Models
{
    public class OrderFilterInput : FilterInput
    {
        public Guid? OrderId { get; set; }
        public string? OrderCode { get; set; }
        public int? OrderStatus { get; set; }
        public int? OrderType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerAccount { get; set; }
    }
}
