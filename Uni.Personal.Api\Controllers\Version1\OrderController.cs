﻿using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Services;
using Uni.Personal.DAL.Models;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/v1/api/[controller]/[action]")]
    [ApiController]
    public class OrderController : UniController
    {
        private readonly IOrderService _orderService;

        public OrderController(IOrderService orderService)
        {
            _orderService = orderService;
        }

        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] OrderFilterInput filter)
        {
            try
            {
                return await _orderService.GetOrderPageAsync(filter);
            }
            catch (Exception ex)
            {
                return new BaseResponse<CommonListPage>(ApiResult.Error, default, ex.Message);
            }
        }

        [HttpGet]
        public async Task<BaseResponse<OrderViewInfo>> GetInfo([FromQuery] Guid? oid)
        {
            try
            {
                return await _orderService.GetOrderInfoAsync(oid);
            }
            catch (Exception ex)
            {
                return new BaseResponse<OrderViewInfo>(ApiResult.Error, default, ex.Message);
            }
        }

        [HttpPost]
        public async Task<BaseResponse<Guid?>> Create([FromBody] OrderViewInfo orderInfo)
        {
            try
            {
                return await _orderService.CreateOrderAsync(orderInfo);
            }
            catch (Exception ex)
            {
                return new BaseResponse<Guid?>(ApiResult.Error, default, ex.Message);
            }
        }

        [HttpPut]
        public async Task<BaseResponse<Guid?>> Update([FromBody] OrderViewInfo orderInfo)
        {
            try
            {
                return await _orderService.UpdateOrderAsync(orderInfo);
            }
            catch (Exception ex)
            {
                return new BaseResponse<Guid?>(ApiResult.Error, null, ex.Message);
            }
        }

        [HttpDelete]
        public async Task<BaseResponse> Delete([FromQuery] Guid? oid)
        {
            try
            {
                return await _orderService.DeleteOrderAsync(oid);
            }
            catch (Exception ex)
            {
                return new BaseResponse(ApiResult.Error, ex.Message);
            }
        }

        [HttpPut]
        public async Task<BaseResponse> UpdateStatus([FromQuery] Guid? oid, [FromQuery] int status)
        {
            try
            {
                return await _orderService.UpdateOrderStatusAsync(oid, status);
            }
            catch (Exception ex)
            {
                return new BaseResponse(ApiResult.Error, ex.Message);
            }
        }

        [HttpGet]
        public async Task<BaseResponse<List<OrderViewInfo>>> GetByCustomer([FromQuery] string customerAccount)
        {
            try
            {
                return await _orderService.GetOrdersByCustomerAsync(customerAccount);
            }
            catch (Exception ex)
            {
                return new BaseResponse<List<OrderViewInfo>>(ApiResult.Error, null, ex.Message);
            }
        }
    }
}
