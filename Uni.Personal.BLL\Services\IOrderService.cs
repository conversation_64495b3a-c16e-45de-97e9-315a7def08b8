using UNI.Model;
using Uni.Personal.DAL.Models;

namespace Uni.Personal.BLL.Services
{
    public interface IOrderService
    {
        Task<BaseResponse<CommonListPage>> GetOrderPageAsync(OrderFilterInput filter);
        Task<BaseResponse<OrderViewInfo>> GetOrderInfoAsync(Guid? oid);
        Task<BaseResponse<Guid?>> CreateOrderAsync(OrderViewInfo orderInfo);
        Task<BaseResponse<Guid?>> UpdateOrderAsync(OrderViewInfo orderInfo);
        Task<BaseResponse> DeleteOrderAsync(Guid? oid);
        Task<BaseResponse> UpdateOrderStatusAsync(Guid? oid, int status);
        Task<BaseResponse<List<OrderViewInfo>>> GetOrdersByCustomerAsync(string customerAccount);
        Task<BaseResponse> ValidateOrderAsync(OrderViewInfo orderInfo);
    }
}
